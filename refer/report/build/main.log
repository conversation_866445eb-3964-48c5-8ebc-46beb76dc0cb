This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2025) (preloaded format=pdflatex 2025.6.3)  29 SEP 2025 22:14
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**/Users/<USER>/Documents/Academic/PhD/Projects/Progress_Report/QE_2025_Nov/QE_report/refer/report/main.tex
(/Users/<USER>/Documents/Academic/PhD/Projects/Progress_Report/QE_2025_Nov/QE_report/refer/report/main.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@section=\count197
\c@subsection=\count198
\c@subsubsection=\count199
\c@paragraph=\count266
\c@subparagraph=\count267
\c@figure=\count268
\c@table=\count269
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
) (/usr/local/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
) (/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count270
\Gm@cntv=\count271
\c@Gm@tempcnt=\count272
\Gm@bindingoffset=\dimen142
\Gm@wd@mp=\dimen143
\Gm@odd@mp=\dimen144
\Gm@even@mp=\dimen145
\Gm@layoutwidth=\dimen146
\Gm@layoutheight=\dimen147
\Gm@layouthoffset=\dimen148
\Gm@layoutvoffset=\dimen149
\Gm@dimlist=\toks18
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen150
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen151
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count273
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count274
\leftroot@=\count275
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count276
\DOTSCASE@=\count277
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen152
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count278
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count279
\dotsspace@=\muskip17
\c@parentequation=\count280
\dspbrk@lvl=\count281
\tag@help=\toks20
\row@=\count282
\column@=\count283
\maxfields@=\count284
\andhelp@=\toks21
\eqnshift@=\dimen153
\alignsep@=\dimen154
\tagshift@=\dimen155
\tagwidth@=\dimen156
\totwidth@=\dimen157
\lineht@=\dimen158
\@envbody=\toks22
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2025/02/06 v3.11 Customized lists
\labelindent=\skip54
\enit@outerparindent=\dimen159
\enit@toks=\toks24
\enit@inbox=\box54
\enit@count@id=\count285
\enitdp@description=\count286
) (/usr/local/texlive/2025/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/dvipsnam.def
File: dvipsnam.def 2016/06/17 v3.0m Driver-dependent file (DPC,SPQR)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2025/02/07 v5.2 Extensive control of page headers and footers
\f@nch@headwidth=\skip55
\f@nch@offset@elh=\skip56
\f@nch@offset@erh=\skip57
\f@nch@offset@olh=\skip58
\f@nch@offset@orh=\skip59
\f@nch@offset@elf=\skip60
\f@nch@offset@erf=\skip61
\f@nch@offset@olf=\skip62
\f@nch@offset@orf=\skip63
\f@nch@height=\skip64
\f@nch@footalignment=\skip65
\f@nch@widthL=\skip66
\f@nch@widthC=\skip67
\f@nch@widthR=\skip68
\@temptokenb=\toks25
) (/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty (/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty (/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks26
\pgfutil@tempdima=\dimen160
\pgfutil@tempdimb=\dimen161
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box55
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
)
\Gin@req@height=\dimen162
\Gin@req@width=\dimen163
) (/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks27
\pgfkeys@temptoks=\toks28
 (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks29
))
\pgf@x=\dimen164
\pgf@y=\dimen165
\pgf@xa=\dimen166
\pgf@ya=\dimen167
\pgf@xb=\dimen168
\pgf@yb=\dimen169
\pgf@xc=\dimen170
\pgf@yc=\dimen171
\pgf@xd=\dimen172
\pgf@yd=\dimen173
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count287
\c@pgf@countb=\count288
\c@pgf@countc=\count289
\c@pgf@countd=\count290
\t@pgf@toka=\toks30
\t@pgf@tokb=\toks31
\t@pgf@tokc=\toks32
\pgf@sys@id@count=\count291
 (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def
 (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count292
\pgfsyssoftpath@bigbuffer@items=\count293
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen174
\pgfmath@count=\count294
\pgfmath@box=\box56
\pgfmath@toks=\toks33
\pgfmath@stack@operand=\toks34
\pgfmath@stack@operation=\toks35
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count295
)) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfint.code.tex) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen175
\pgf@picmaxx=\dimen176
\pgf@picminy=\dimen177
\pgf@picmaxy=\dimen178
\pgf@pathminx=\dimen179
\pgf@pathmaxx=\dimen180
\pgf@pathminy=\dimen181
\pgf@pathmaxy=\dimen182
\pgf@xx=\dimen183
\pgf@xy=\dimen184
\pgf@yx=\dimen185
\pgf@yy=\dimen186
\pgf@zx=\dimen187
\pgf@zy=\dimen188
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen189
\pgf@path@lasty=\dimen190
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen191
\pgf@shorten@start@additional=\dimen192
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box57
\pgf@hbox=\box58
\pgf@layerbox@main=\box59
\pgf@picture@serial@count=\count296
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen193
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen194
\pgf@pt@y=\dimen195
\pgf@pt@temp=\dimen196
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen197
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen198
\pgf@sys@shading@range@num=\count297
\pgf@shadingcount=\count298
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box60
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box61
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen199
\pgf@nodesepend=\dimen256
) (/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/utilities/pgffor.sty (/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex)) (/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/math/pgfmath.sty (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen257
\pgffor@skip=\dimen258
\pgffor@stack=\toks36
\pgffor@toks=\toks37
)) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count299
\pgfplotmarksize=\dimen259
)
\tikz@lastx=\dimen260
\tikz@lasty=\dimen261
\tikz@lastxsaved=\dimen262
\tikz@lastysaved=\dimen263
\tikz@lastmovetox=\dimen264
\tikz@lastmovetoy=\dimen265
\tikzleveldistance=\dimen266
\tikzsiblingdistance=\dimen267
\tikz@figbox=\box62
\tikz@figbox@bg=\box63
\tikz@tempbox=\box64
\tikz@tempbox@bg=\box65
\tikztreelevel=\count300
\tikznumberofchildren=\count301
\tikznumberofcurrentchild=\count302
\tikz@fig@count=\count303
 (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count304
\pgfmatrixcurrentcolumn=\count305
\pgf@matrix@numberofcolumns=\count306
)
\tikz@expandcount=\count307
 (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/biblatex.sty
Package: biblatex 2024/03/21 v3.20 programmable bibliographies (PK/MW)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
) (/usr/local/texlive/2025/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count308
) (/usr/local/texlive/2025/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/logreq/logreq.sty
Package: logreq 2010/08/04 v1.0 xml request logger
\lrq@indent=\count309
 (/usr/local/texlive/2025/texmf-dist/tex/latex/logreq/logreq.def
File: logreq.def 2010/08/04 v1.0 logreq spec v1.0
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
) (/usr/local/texlive/2025/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
\c@tabx@nest=\count310
\c@listtotal=\count311
\c@listcount=\count312
\c@liststart=\count313
\c@liststop=\count314
\c@citecount=\count315
\c@citetotal=\count316
\c@multicitecount=\count317
\c@multicitetotal=\count318
\c@instcount=\count319
\c@maxnames=\count320
\c@minnames=\count321
\c@maxitems=\count322
\c@minitems=\count323
\c@citecounter=\count324
\c@maxcitecounter=\count325
\c@savedcitecounter=\count326
\c@uniquelist=\count327
\c@uniquename=\count328
\c@refsection=\count329
\c@refsegment=\count330
\c@maxextratitle=\count331
\c@maxextratitleyear=\count332
\c@maxextraname=\count333
\c@maxextradate=\count334
\c@maxextraalpha=\count335
\c@abbrvpenalty=\count336
\c@highnamepenalty=\count337
\c@lownamepenalty=\count338
\c@maxparens=\count339
\c@parenlevel=\count340
\blx@tempcnta=\count341
\blx@tempcntb=\count342
\blx@tempcntc=\count343
\c@blx@maxsection=\count344
\blx@maxsegment@0=\count345
\blx@notetype=\count346
\blx@parenlevel@text=\count347
\blx@parenlevel@foot=\count348
\blx@sectionciteorder@0=\count349
\blx@sectionciteorderinternal@0=\count350
\blx@entrysetcounter=\count351
\blx@biblioinstance=\count352
\labelnumberwidth=\skip69
\labelalphawidth=\skip70
\biblabelsep=\skip71
\bibitemsep=\skip72
\bibnamesep=\skip73
\bibinitsep=\skip74
\bibparsep=\skip75
\bibhang=\skip76
\blx@bcfin=\read3
\blx@bcfout=\write4
\blx@langwohyphens=\language90
\c@mincomprange=\count353
\c@maxcomprange=\count354
\c@mincompwidth=\count355
Package biblatex Info: Trying to load biblatex default data model...
Package biblatex Info: ... file 'blx-dm.def' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/blx-dm.def
File: blx-dm.def 2024/03/21 v3.20 biblatex datamodel (PK/MW)
)
Package biblatex Info: Trying to load biblatex style data model...
Package biblatex Info: ... file 'phys.dbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex-phys/phys.dbx
File: phys.dbx 2024/07/26 v1.1c biblatex database model extension
)
Package biblatex Info: Trying to load biblatex custom data model...
Package biblatex Info: ... file 'biblatex-dm.cfg' not found.
\c@afterword=\count356
\c@savedafterword=\count357
\c@annotator=\count358
\c@savedannotator=\count359
\c@author=\count360
\c@savedauthor=\count361
\c@bookauthor=\count362
\c@savedbookauthor=\count363
\c@commentator=\count364
\c@savedcommentator=\count365
\c@editor=\count366
\c@savededitor=\count367
\c@editora=\count368
\c@savededitora=\count369
\c@editorb=\count370
\c@savededitorb=\count371
\c@editorc=\count372
\c@savededitorc=\count373
\c@foreword=\count374
\c@savedforeword=\count375
\c@holder=\count376
\c@savedholder=\count377
\c@introduction=\count378
\c@savedintroduction=\count379
\c@namea=\count380
\c@savednamea=\count381
\c@nameb=\count382
\c@savednameb=\count383
\c@namec=\count384
\c@savednamec=\count385
\c@translator=\count386
\c@savedtranslator=\count387
\c@shortauthor=\count388
\c@savedshortauthor=\count389
\c@shorteditor=\count390
\c@savedshorteditor=\count391
\c@labelname=\count392
\c@savedlabelname=\count393
\c@institution=\count394
\c@savedinstitution=\count395
\c@lista=\count396
\c@savedlista=\count397
\c@listb=\count398
\c@savedlistb=\count399
\c@listc=\count400
\c@savedlistc=\count401
\c@listd=\count402
\c@savedlistd=\count403
\c@liste=\count404
\c@savedliste=\count405
\c@listf=\count406
\c@savedlistf=\count407
\c@location=\count408
\c@savedlocation=\count409
\c@organization=\count410
\c@savedorganization=\count411
\c@origlocation=\count412
\c@savedoriglocation=\count413
\c@origpublisher=\count414
\c@savedorigpublisher=\count415
\c@publisher=\count416
\c@savedpublisher=\count417
\c@language=\count418
\c@savedlanguage=\count419
\c@origlanguage=\count420
\c@savedoriglanguage=\count421
\c@pageref=\count422
\c@savedpageref=\count423
\shorthandwidth=\skip77
\shortjournalwidth=\skip78
\shortserieswidth=\skip79
\shorttitlewidth=\skip80
\shortauthorwidth=\skip81
\shorteditorwidth=\skip82
\locallabelnumberwidth=\skip83
\locallabelalphawidth=\skip84
\localshorthandwidth=\skip85
\localshortjournalwidth=\skip86
\localshortserieswidth=\skip87
\localshorttitlewidth=\skip88
\localshortauthorwidth=\skip89
\localshorteditorwidth=\skip90
Package biblatex Info: Trying to load compatibility code...
Package biblatex Info: ... file 'blx-compat.def' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/blx-compat.def
File: blx-compat.def 2024/03/21 v3.20 biblatex compatibility (PK/MW)
)
Package biblatex Info: Trying to load generic definitions...
Package biblatex Info: ... file 'biblatex.def' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/biblatex.def
File: biblatex.def 2024/03/21 v3.20 biblatex compatibility (PK/MW)
\c@textcitecount=\count424
\c@textcitetotal=\count425
\c@textcitemaxnames=\count426
\c@biburlbigbreakpenalty=\count427
\c@biburlbreakpenalty=\count428
\c@biburlnumpenalty=\count429
\c@biburlucpenalty=\count430
\c@biburllcpenalty=\count431
\biburlbigskip=\muskip19
\biburlnumskip=\muskip20
\biburlucskip=\muskip21
\biburllcskip=\muskip22
\c@smartand=\count432
)
Package biblatex Info: Trying to load bibliography style 'phys'...
Package biblatex Info: ... file 'phys.bbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex-phys/phys.bbx
File: phys.bbx 2024/07/26 v1.1c biblatex bibliography style
Package biblatex Info: Trying to load bibliography style 'numeric-comp'...
Package biblatex Info: ... file 'numeric-comp.bbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/bbx/numeric-comp.bbx
File: numeric-comp.bbx 2024/03/21 v3.20 biblatex bibliography style (PK/MW)
Package biblatex Info: Trying to load bibliography style 'numeric'...
Package biblatex Info: ... file 'numeric.bbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/bbx/numeric.bbx
File: numeric.bbx 2024/03/21 v3.20 biblatex bibliography style (PK/MW)
Package biblatex Info: Trying to load bibliography style 'standard'...
Package biblatex Info: ... file 'standard.bbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/bbx/standard.bbx
File: standard.bbx 2024/03/21 v3.20 biblatex bibliography style (PK/MW)
\c@bbx:relatedcount=\count433
\c@bbx:relatedtotal=\count434
))))
Package biblatex Info: Trying to load citation style 'phys'...
Package biblatex Info: ... file 'phys.cbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex-phys/phys.cbx
File: phys.cbx 2024/07/26 v1.1c biblatex citation style
Package biblatex Info: Trying to load citation style 'numeric-comp'...
Package biblatex Info: ... file 'numeric-comp.cbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/cbx/numeric-comp.cbx
File: numeric-comp.cbx 2024/03/21 v3.20 biblatex citation style (PK/MW)
\c@cbx@tempcnta=\count435
\c@cbx@tempcntb=\count436
\c@cbx@tempcntc=\count437
\c@cbx@tempcntd=\count438
Package biblatex Info: Redefining '\cite'.
Package biblatex Info: Redefining '\parencite'.
Package biblatex Info: Redefining '\footcite'.
Package biblatex Info: Redefining '\footcitetext'.
Package biblatex Info: Redefining '\smartcite'.
Package biblatex Info: Redefining '\supercite'.
Package biblatex Info: Redefining '\textcite'.
Package biblatex Info: Redefining '\textcites'.
Package biblatex Info: Redefining '\cites'.
Package biblatex Info: Redefining '\parencites'.
Package biblatex Info: Redefining '\smartcites'.
))
Package biblatex Info: Trying to load configuration file...
Package biblatex Info: ... file 'biblatex.cfg' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/biblatex.cfg
File: biblatex.cfg 
)
Package biblatex Info: Input encoding 'utf8' detected.
Package biblatex Info: Document encoding is UTF8 ....
 (/usr/local/texlive/2025/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-01-18 L3 programming layer (loader) 
 (/usr/local/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count439
\l__pdf_internal_box=\box66
))
Package biblatex Info: ... and expl3
(biblatex)             2025-01-18 L3 programming layer (loader) 
(biblatex)             is new enough (at least 2020/04/06),
(biblatex)             setting 'casechanger=expl3'.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/blx-case-expl3.sty (/usr/local/texlive/2025/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-08-16 L3 Experimental document command parser
)
Package: blx-case-expl3 2024/03/21 v3.20 expl3 case changing code for biblatex
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/caption/subcaption.sty
Package: subcaption 2023/07/28 v1.6b Sub-captions (AR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen268
\captionmargin=\dimen269
\caption@leftmargin=\dimen270
\caption@rightmargin=\dimen271
\caption@width=\dimen272
\caption@indent=\dimen273
\caption@parindent=\dimen274
\caption@hangindent=\dimen275
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count440
\c@continuedfloat=\count441
)
Package caption Info: New subtype `subfigure' on input line 238.
\c@subfigure=\count442
Package caption Info: New subtype `subtable' on input line 238.
\c@subtable=\count443
)
\@quotelevel=\count444
\@quotereset=\count445

No file main.aux.
\openout1 = `main.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 30.
LaTeX Font Info:    ... okay on input line 30.
*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(42.67912pt, 512.14963pt, 42.67912pt)
* v-part:(T,H,B)=(42.67912pt, 759.6886pt, 42.67912pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=512.14963pt
* \textheight=759.6886pt
* \oddsidemargin=-29.59087pt
* \evensidemargin=-29.59087pt
* \topmargin=-66.59087pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=30.0pt
* \marginparwidth=59.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

(/usr/local/texlive/2025/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count446
\scratchdimen=\dimen276
\scratchbox=\box67
\nofMPsegments=\count447
\nofMParguments=\count448
\everyMPshowfont=\toks38
\MPscratchCnt=\count449
\MPscratchDim=\dimen277
\MPnumerator=\count450
\makeMPintoPDFobject=\count451
\everyMPtoPDFconversion=\toks39
) (/usr/local/texlive/2025/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
Package biblatex Info: Trying to load language 'english'...
Package biblatex Info: ... file 'english.lbx' found.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/lbx/english.lbx
File: english.lbx 2024/03/21 v3.20 biblatex localization (PK/MW)
)
Package biblatex Info: Input encoding 'utf8' detected.
Package biblatex Info: Automatic encoding selection.
(biblatex)             Assuming data encoding 'utf8'.
\openout4 = `main.bcf'.

Package biblatex Info: Trying to load bibliographic data...
Package biblatex Info: ... file 'main.bbl' found.
 (build/main.bbl)
Package biblatex Info: Reference section=0 on input line 30.
Package biblatex Info: Reference segment=0 on input line 30.
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: End \AtBeginDocument code.
LaTeX Font Info:    Trying to load font information for U+msa on input line 32.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 32.
 (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

LaTeX Warning: Reference `fig:lattice' on page 1 undefined on input line 48.

<lattice.png, id=4, 385.44pt x 385.44pt>
File: lattice.png Graphic file (type png)
<use lattice.png>
Package pdftex.def Info: lattice.png  used on input line 52.
(pdftex.def)             Requested size: 102.42836pt x 102.42342pt.

LaTeX Warning: Reference `fig:phase' on page 1 undefined on input line 61.

<phase.png, id=5, 906.38625pt x 355.3275pt>
File: phase.png Graphic file (type png)
<use phase.png>
Package pdftex.def Info: phase.png  used on input line 65.
(pdftex.def)             Requested size: 256.07481pt x 100.38562pt.


[1

{/usr/local/texlive/2025/texmf-var/fonts/map/pdftex/updmap/pdftex.map} <./lattice.png (PNG copy)>]

LaTeX Warning: Reference `fig:prophase' on page 2 undefined on input line 99.

<prophase.jpeg, id=24, 1988.42876pt x 1116.17pt>
File: prophase.jpeg Graphic file (type jpg)
<use prophase.jpeg>
Package pdftex.def Info: prophase.jpeg  used on input line 102.
(pdftex.def)             Requested size: 204.85672pt x 114.97871pt.



Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[2{/usr/local/texlive/2025/texmf-dist/fonts/enc/dvips/cm-super/cm-super-ts1.enc} <./phase.png> <./prophase.jpeg>]

LaTeX Warning: Reference `fig:energy' on page 3 undefined on input line 117.

<energy1.png, id=30, 859.2903pt x 570.933pt>
File: energy1.png Graphic file (type png)
<use energy1.png>
Package pdftex.def Info: energy1.png  used on input line 124.
(pdftex.def)             Requested size: 256.07481pt x 170.13998pt.
<energy2.png, id=31, 859.2903pt x 570.933pt>
File: energy2.png Graphic file (type png)
<use energy2.png>
Package pdftex.def Info: energy2.png  used on input line 130.
(pdftex.def)             Requested size: 256.07481pt x 170.13998pt.
<image1.png, id=32, 715.473pt x 426.393pt>
File: image1.png Graphic file (type png)
<use image1.png>
Package pdftex.def Info: image1.png  used on input line 167.
(pdftex.def)             Requested size: 179.2555pt x 106.8257pt.
<image2.png, id=33, 715.473pt x 426.393pt>
File: image2.png Graphic file (type png)
<use image2.png>
Package pdftex.def Info: image2.png  used on input line 174.
(pdftex.def)             Requested size: 179.2555pt x 106.8257pt.
<image3.png, id=34, 715.473pt x 426.393pt>
File: image3.png Graphic file (type png)
<use image3.png>
Package pdftex.def Info: image3.png  used on input line 181.
(pdftex.def)             Requested size: 179.2555pt x 106.8257pt.

LaTeX Warning: Reference `fig:ratio' on page 3 undefined on input line 186.




Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[3 <./energy1.png> <./energy2.png>]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[4 <./image1.png> <./image2.png> <./image3.png>]
/Users/<USER>/Documents/Academic/PhD/Projects/Progress_Report/QE_2025_Nov/QE_report/refer/report/main.tex:259: Missing $ inserted.
<inserted text> 
                $
l.259 
      
I've inserted a begin-math/end-math symbol since I think
you left one out. Proceed, with fingers crossed.

/Users/<USER>/Documents/Academic/PhD/Projects/Progress_Report/QE_2025_Nov/QE_report/refer/report/main.tex:259: Missing $ inserted.
<inserted text> 
                $
l.259 
      
I've inserted something that you may have forgotten.
(See the <inserted text> above.)
With luck, this will get me unwedged. But if you
really didn't forget anything, try typing `2' now; then
my insertion and my current dilemma will both disappear.




Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 13.59999pt, for example:
(fancyhdr)                \setlength{\headheight}{13.59999pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-1.59999pt}.

[5] (build/main.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********


LaTeX Warning: There were undefined references.


LaTeX Warning: Label(s) may have changed. Rerun to get cross-references right.


Package biblatex Warning: Please (re)run Biber on the file:
(biblatex)                main
(biblatex)                and rerun LaTeX afterwards.

Package logreq Info: Writing requests to 'main.run.xml'.
\openout1 = `main.run.xml'.

 ) 
Here is how much of TeX's memory you used:
 24126 strings out of 473190
 478455 string characters out of 5715801
 1284242 words of memory out of 5000000
 47037 multiletter control sequences out of 15000+600000
 567986 words of font info for 70 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 102i,11n,107p,789b,1463s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx12.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx8.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi8.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr12.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr17.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmr8.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy6.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy8.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmtt10.pfb></usr/local/texlive/2025/texmf-dist/fonts/type1/public/cm-super/sfrm1095.pfb>
Output written on build/main.pdf (5 pages, 1427197 bytes).
PDF statistics:
 114 PDF objects out of 1000 (max. 8388607)
 62 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 53 words of extra memory for PDF output out of 10000 (max. 10000000)

