This chapter outlines future research directions building on the established work. From the two perspectives of scanning physical parameters and methodological improvements, we propose a systematic research program aimed at a characterization of deconfined quantum criticality in the extended Shastry–Sutherland model, and at providing a better understanding regarding the existence and nature of deconfined quantum critical points (DQCP) in frustrated magnets.

\section{Strategy for Scanning Physical Parameters}

\subsection{Identifying the DQCP Window in Small Systems}

Based on the parameter transfer-learning methodology developed in Chapter 4, we will first perform a systematic scan of the parameter space in computationally feasible small systems to identify potential DQCP windows:

\begin{enumerate}
\item \textbf{Initial grid scan}: Conduct a coarse-grained parameter scan on 4x4x4 and 5x5x4 systems:
   \begin{itemize}
   \item Systematically scan the two-dimensional parameter space $(\frac{J'}{J'+Q}, \frac{J}{J'+Q})$
   \item Focus on the lines close to the pure J-Q model ($\frac{J'}{J'+Q} = 0$).
   \item Use order parameters and correlation ratios to identify signals of phase transitions
   \end{itemize}

\item \textbf{Simultaneous monitoring of order parameters}: Compute multiple order parameters at each parameter point to identify DQCP candidates:
   \begin{enumerate}
      \item \textbf{Antiferromagnetic order parameter}:
      \begin{itemize}
         \item Structure factor: $S(\mathbf{k}) = \frac{1}{N}\sum_{r} e^{i\mathbf{k}\cdot\mathbf{r}} \langle \mathbf{S}_r \cdot \mathbf{S}_0 \rangle$.
      \end{itemize}
      
      \item \textbf{Dimer order parameter}:
      \begin{itemize}
         \item Structure factor: $D(\mathbf{k}) = \frac{1}{N}\sum_{r} e^{i\mathbf{k}\cdot\mathbf{r}} \langle(\mathbf{S}_r \cdot \mathbf{S}_{r+\hat{x}})(\mathbf{S}_{0} \cdot \mathbf{S}_{\hat{x}})\rangle$.
      \end{itemize}

      \item \textbf{Plaquette order parameter}:
      \begin{itemize}
         \item Structure factor: $C(\mathbf{k}) = \frac{1}{N}\sum_{r} e^{i\mathbf{k}\cdot\mathbf{r}} \langle(\hat{P}_r + \hat{P}^{-1}_r)(\hat{P}_{0} + \hat{P}^{-1}_{0})\rangle$.
         \item $\hat{P}_r$ is plaquette cyclic permutation operator
      \end{itemize}

      
   \end{enumerate}
\item \textbf{Criteria for defining the DQCP window}: Determine candidate regions based on theoretical framework established in Chapter 2:
   \begin{itemize}
   \item Simultaneous scaling of competing order parameters (antiferromagnetic and VBS) near the same parameter point: $m_N^2(L) \sim m_{VBS}^2(L) \sim L^{-(1+\eta)}$
   \item Unified correlation length divergence across distinct order-parameter channels
   \item Signatures of emergent enhanced symmetries (e.g., O(4) or SO(5) symmetry unifying competing orders)
   \item Evidence of fractionalized excitations (deconfined spinons) at the critical point
   \item Finite-size scaling consistent with DQCP critical exponents, accounting for potential deviations from conformal field theory predictions
   \item Characteristic signatures of logarithmic corrections arising from monopole operators, while recognizing that DQCP may not fully conform to traditional conformal field theory descriptions
   \end{itemize}
\end{enumerate}

\subsection{Large-System Extension and Validation}

After identifying DQCP windows in small systems, we will systematically extend to larger systems to validate the thermodynamic-limit nature of the critical behavior:

\begin{enumerate}
\item \textbf{Target system sizes}: Gradually enlarge system sizes to enable reliable finite-size scaling:
   \begin{itemize}
   \item Target sizes: 6×6×4 (144 spins), 7×7×4 (196 spins), 8×8×4 (256 spins), and potentially 9×9×4 (324 spins)
   \item Develop neural-network architectures optimized for large systems to enhance parameter efficiency
   \item Implement advanced sampling techniques to maintain statistical precision in larger systems
   \end{itemize}

\item \textbf{Expected physical signatures}: Within confirmed DQCP windows, large systems should exhibit:
   \begin{itemize}
   \item Simultaneous critical scaling of multiple order parameters
   \item Unified divergence of correlation lengths across different channels
   \item System-size independence of critical exponents
   \item Signals of emergent enhanced symmetry (e.g., O(4) or SO(5))
   \end{itemize}

\item \textbf{Confirmation criteria for DQCP}: Confirm deconfined quantum criticality through the following:
\end{enumerate}

\section{Methodological Improvements and Validation}

\subsection{Validating the Accuracy of Parameter Transfer Learning}

Building on the parameter transfer-learning approach established in Chapter 4, we will further assess its accuracy and reliability near phase transitions:

\begin{enumerate}
\item \textbf{Transfer across phase boundaries}: Evaluate performance at the transition point:
   \begin{itemize}
   \item Transfer from parameters at the edge of one phase to those of another and test whether ground-state changes at the transition are captured accurately
   \item Analyze convergence behavior when the ground state undergoes strong qualitative changes
   \item Compare accuracy against training from scratch in the vicinity of the phase transition
   \end{itemize}

\item \textbf{Sensitivity to phase transitions}: Quantify sensitivity to criticality:
   \begin{itemize}
   \item Perform high-density parameter scans near known transition points
   \item Verify that transfer learning faithfully reproduces critical behavior
   \item Assess the capability to distinguish first-order from continuous transitions
   \end{itemize}

\item \textbf{Ground-state fidelity and consistency}: Validate physical accuracy through multiple diagnostics:
   \begin{itemize}
   \item Compare energy-convergence accuracy with training from scratch
   \item Check consistency of order-parameter estimates
   \item Validate correlation functions
   \end{itemize}
\end{enumerate}

\subsection{Order-Parameter Extrapolation Methods}

Following Viteritti et al.'s sophisticated order-parameter extrapolation framework in Transformer wave functions\cite{viteritti2024transformer}, we implement two primary extrapolation strategies to achieve precise estimates of order parameters in the thermodynamic limit:

\begin{enumerate}
\item \textbf{Finite-size extrapolation methodology}: We employ systematic extrapolations following Viteritti et al.'s established protocols:
   \begin{itemize}
   \item \textbf{Standard finite-size scaling}: Use the polynomial form for ordered phases:
   \begin{equation}
   m^2(L) = m_0^2 + \frac{A_1}{L} + \frac{A_2}{L^2} + \mathcal{O}(L^{-3})
   \end{equation}
   where $m_0$ is the order parameter in the thermodynamic limit
   \item \textbf{Critical scaling form}: For disordered phases near criticality, employ the power-law scaling:
   \begin{equation}
   m^2(L) \sim L^{-(1+\eta)}
   \end{equation}
   where $\eta$ is the anomalous dimension ($\eta \approx 0.3$ for the Shastry-Sutherland model)
   \item \textbf{Cross-validation approach}: Utilize both polynomial and critical forms to determine the appropriate scaling behavior
   \item \textbf{Variance-based extrapolation}: Perform complementary extrapolations as a function of energy variance to validate results independent of network architecture
   \end{itemize}

\item \textbf{Order-parameter extrapolation strategy}: Implement Viteritti et al.'s dual extrapolation approach:
   \begin{itemize}
   \item \textbf{Layer-depth extrapolation}: Extract order parameters $m^2(L, n_l)$ and $m_p(L, n_l)$ for varying numbers of Transformer layers $n_l = 2, 4, 8$, then extrapolate to infinite depth.
   \item \textbf{Feature-dimensional extrapolation}: Extract order parameters $m^2(L, d_f)$ and $m_p(L, d_f)$ for varying feature dimensions $d_f = 2, 4, 8$, then extrapolate to infinite dimension.
   \item \textbf{Energy variance extrapolation}: Alternatively, extrapolate order parameters as a function of energy variance per site:
   \begin{equation}
   m^2(\text{Var}[E]/N) \to m^2(\text{Var}[E]/N = 0)
   \end{equation}
   This approach provides architecture and training-independent validation of results
   \item \textbf{Consistency validation}: Verify that both extrapolation methods yield compatible results within error bars
   \item \textbf{Resampling uncertainty quantification}: Estimate error bars using Gaussian noise resampling techniques to properly characterize extrapolation uncertainties
   \item \textbf{Multi-size analysis}: Apply extrapolations systematically across system sizes $L = 6$ to $L = 18$ to ensure robust thermodynamic limit estimates
   \end{itemize}
\end{enumerate}

These methodologies enable precise determination of phase boundaries and characterization of the intermediate spin liquid phase, providing reliable estimates of critical points through systematic finite-size scaling analysis combined with deep neural network optimization protocols.

\subsection{Optimization of Neural-Network Architectures}

Leveraging insights from Chapters 3 and 4, we will further optimize neural-network architectures to improve computational efficiency and accuracy:

\begin{enumerate}
\item \textbf{Hybrid architectures}: Combine strengths of different approaches:
   \begin{itemize}
   \item Convolution–Transformer wave functions (CTWF) with optimized attention patterns for frustrated lattices
   \item Multi-scale architectures with hierarchical representations: local plaquettes $\to$ intermediate clusters $\to$ global correlations
   \item Foundational neural quantum states pre-trained on simpler models (e.g., pure Heisenberg) and fine-tuned for extended parameter spaces
   \end{itemize}

\item \textbf{Symmetry-preserving design}: Strengthen symmetry integration in architectures:
   \begin{itemize}
   \item Design architectures that respect conservation laws (total spin, particle number)
   \end{itemize}

\item \textbf{Parameter-efficiency optimizations}: Achieve better scaling with system size:
   \begin{itemize}
   \item Investigate parameter-sharing schemes to reduce total parameter counts
   \item Develop compression techniques for large neural quantum states
   \item Implement adaptive architectures with problem-dependent complexity
   \end{itemize}
\end{enumerate}


\section{Computational Implementation Plan}

\subsection{Phased Research Objectives}

Building on the established methodological foundation, we propose the following structured implementation plan:

\begin{enumerate}
\item \textbf{Phase I: Identify DQCP windows in small systems}:
   \begin{itemize}
   \item Complete a comprehensive scan of the parameter space on 4×4×4 and 5×5×4 systems
   \item Ensure continuity and accuracy via improved parameter transfer-learning
   \item Identify and validate candidate DQCP regions
   \item Standardize the order-parameter extrapolation workflow
   \end{itemize}

\item \textbf{Phase II: Large-system validation and precise characterization}:
   \begin{itemize}
   \item Extend computations to larger systems
   \item Perform high-precision finite-size scaling within confirmed DQCP windows
   \item Apply extrapolation techniques to estimate thermodynamic-limit values accurately
   \item Validate transfer-learning accuracy near phase transitions
   \end{itemize}

\item \textbf{Phase III: Methodological validation and generalization}:
   \begin{itemize}
   \item Complete a comprehensive validation of transfer learning across phase boundaries
   \item Establish protocols for identifying and characterizing DQCP
   \item Provide a methodological foundation for other frustrated quantum systems
   \item Summarize numerical signatures and identification criteria of deconfined quantum criticality
   \end{itemize}
\end{enumerate}

\subsection{Expected Challenges and Solutions}

Potential challenges during implementation and corresponding mitigations:

\begin{enumerate}
\item \textbf{Computational-resource challenges}:
   \begin{itemize}
   \item Memory and time demands for large systems
   \item Mitigations: optimize architectures, implement distributed computing, and use efficient sampling
   \end{itemize}

\item \textbf{Methodological challenges}:
   \begin{itemize}
   \item Stability of transfer learning across sharp transitions
   \item Mitigations: develop adaptive transfer strategies and multi-pronged validation
   \end{itemize}

\item \textbf{Physical-interpretation challenges}:
   \begin{itemize}
   \item Clear identification and validation of DQCP signatures
   \item Mitigations: establish multiple criteria and benchmark against theoretical expectations
   \end{itemize}
\end{enumerate}