This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2025) (preloaded format=pdflatex 2025.6.3)  29 SEP 2025 22:11
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**test_ch05.tex
(./test_ch05.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
! Text line contains an invalid character.
l.1 \documentclass{report}^^@
                             sepackage{amsmath,amssymb,physics,graphicx}^^He...
A funny symbol that I can't read has just been input.
Continue, and I'll forget that it ever happened.

(/usr/local/texlive/2025/texmf-dist/tex/latex/base/report.cls
Document Class: report 2024/06/29 v1.4n Standard LaTeX document class
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@chapter=\count197
\c@section=\count198
\c@subsection=\count199
\c@subsubsection=\count266
\c@paragraph=\count267
\c@subparagraph=\count268
\c@figure=\count269
\c@table=\count270
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)

! LaTeX Error: Missing \begin{document}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.1 \documentclass{report}^^@s
                              epackage{amsmath,amssymb,physics,graphicx}^^He...

You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


! LaTeX Error: Unicode character ^^H (U+0008)
               not set up for use with LaTeX.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.1 ...ackage{amsmath,amssymb,physics,graphicx}^^H
                                                  egin{document}\input{../ch...

You may provide a definition with
\DeclareUnicodeCharacter 

(../chapters/chapter05.tex
Overfull \hbox (4.33403pt too wide) in paragraph at lines 1--2
[]\OT1/cmr/m/n/10 sepackageamsmath,amssymb,physics,graphicxegindocumentThis cha
p-ter out-
 []


! LaTeX Error: Missing \begin{document}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.3 \section
            {Strategy for Scanning Physical Parameters}
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


! LaTeX Error: Missing \begin{document}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.3 ...{Strategy for Scanning Physical Parameters}
                                                  
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <7> on input line 12.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <5> on input line 12.

Overfull \hbox (7.53079pt too wide) in paragraph at lines 12--13
[]\OT1/cmr/m/n/10 Systematically scan the two-dimensional pa-ram-e-ter space $(
[]\OML/cmm/m/it/10 ; []\OT1/cmr/m/n/10 )$ 
 []


Overfull \hbox (39.2103pt too wide) in paragraph at lines 19--20
[]\OT1/cmr/m/n/10 N^^Seel or-der pa-ram-e-ter: $\OML/cmm/m/it/10 m[]\OT1/cmr/m/
n/10 (\OML/cmm/m/it/10 L\OT1/cmr/m/n/10 ) = []$ where $\OML/cmm/m/it/10 S\OT1/c
mr/m/n/10 (\OT1/cmr/bx/n/10 k\OT1/cmr/m/n/10 ) = [] [][] \OML/cmm/m/it/10 e[]\O
MS/cmsy/m/n/10 h\OML/cmm/m/it/10 S[]S[]\OMS/cmsy/m/n/10 i$
 []

! Undefined control sequence.
l.20 ... O_p \rangle^2$ where $O_p = \sum_{\square
                                                  } (-1)^{x+y} (\mathbf{S}_1...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

! Undefined control sequence.
<recently read> \square 
                        
l.20 ...cdot \mathbf{S}_4)$ for plaquette $\square
                                                  $
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


Overfull \hbox (43.93188pt too wide) in paragraph at lines 21--22
[]\OT1/cmr/m/n/10 Dimer VBS or-der pa-ram-e-ter: $\OML/cmm/m/it/10 m[]\OT1/cmr/
m/n/10 (\OML/cmm/m/it/10 L\OT1/cmr/m/n/10 ) = [] [][]$ 
 []


Overfull \hbox (105.56955pt too wide) in paragraph at lines 22--23
[]\OT1/cmr/m/n/10 Diagonal dimer or-der pa-ram-e-ter: $\OML/cmm/m/it/10 m[]\OT1
/cmr/m/n/10 (\OML/cmm/m/it/10 L\OT1/cmr/m/n/10 ) = [] [][]$ 
 []



[1

{/usr/local/texlive/2025/texmf-var/fonts/map/pdftex/updmap/pdftex.map}
\@writefile{toc}{\contentsline {section}{\numberline {0.1}Strategy for Scanning
 Physical Parameters}{1}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {0.1.1}Identifying the 
DQCP Window in Small Systems}{1}{}\protected@file@percent }
{/usr/local/texlive/2025/texmf-dist/fonts/enc/dvips/cm-super/cm-super-ts1.enc}
\@writefile{toc}{\contentsline {subsection}{\numberline {0.1.2}Large-System Ext
ension and Validation}{1}{}\protected@file@percent }
]
Overfull \hbox (8.50558pt too wide) in paragraph at lines 66--66
[]\OT1/cmr/bx/n/12 Validating the Ac-cu-racy of Pa-ram-e-ter Trans-fer Learn-
 []


Overfull \hbox (3.69499pt too wide) in paragraph at lines 74--75
[]\OT1/cmr/m/n/10 Analyze con-ver-gence be-hav-ior when the ground state un-der
-goes strong
 []

\citation{viteritti2024transformer}

LaTeX Warning: Citation `viteritti2024transformer' on page 2 undefined on input
 line 95.



[2
\@writefile{toc}{\contentsline {section}{\numberline {0.2}Methodological Improv
ements and Validation}{2}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {0.2.1}Validating the A
ccuracy of Parameter Transfer Learning}{2}{}\protected@file@percent }
]
Overfull \hbox (1.13948pt too wide) in paragraph at lines 103--104
[]\OT1/cmr/m/n/10 Use Bayesian anal-y-sis for pa-ram-e-ter es-ti-ma-tion and un
-cer-tainty quan-
 []



[3
\@writefile{toc}{\contentsline {subsection}{\numberline {0.2.2}Order-Parameter 
Extrapolation Methods}{3}{}\protected@file@percent }
]
\citation{viteritti2024transformer}

LaTeX Warning: Citation `viteritti2024transformer' on page 4 undefined on input
 line 131.


Overfull \hbox (2.30608pt too wide) in paragraph at lines 131--132
[]\OT1/cmr/m/n/10 GCNN{Transformer hy-brids fol-low-ing Vi-teritti et al.'s arc
hitecture[[]]:
 []


Overfull \hbox (9.66718pt too wide) in paragraph at lines 146--147
[]\OT1/cmr/m/n/10 Investigate parameter-sharing schemes to re-duce to-tal pa-ra
m-e-ter counts 
 []



[4
\@writefile{toc}{\contentsline {subsection}{\numberline {0.2.3}Optimization of 
Neural-Network Architectures}{4}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {0.2.4}Training and Opt
imization Enhancements}{4}{}\protected@file@percent }
]

[5
\@writefile{toc}{\contentsline {subsection}{\numberline {0.2.5}Sampling and Sta
tistical Analysis}{5}{}\protected@file@percent }
]
Overfull \hbox (0.4728pt too wide) in paragraph at lines 231--232
[]\OT1/cmr/m/n/10 Complete a com-pre-hen-sive val-i-da-tion of trans-fer learn-
ing across phase
 []



[6
\@writefile{toc}{\contentsline {section}{\numberline {0.3}Computational Impleme
ntation Plan}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {0.3.1}Phased Research 
Objectives}{6}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {0.3.2}Expected Challen
ges and Solutions}{6}{}\protected@file@percent }
])

! LaTeX Error: Unicode character ^^[ (U+001B)
               not set up for use with LaTeX.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.1 ...n{document}\input{../chapters/chapter05}^^[
                                                  nd{document}
You may provide a definition with
\DeclareUnicodeCharacter 

)
! Emergency stop.
<*> test_ch05.tex
                 
*** (job aborted, no legal \end found)

 
Here is how much of TeX's memory you used:
 287 strings out of 473190
 3665 string characters out of 5715801
 414566 words of memory out of 5000000
 23619 multiletter control sequences out of 15000+600000
 561309 words of font info for 44 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 35i,6n,38p,629b,261s stack positions out of 10000i,1000n,20000p,200000b,200000s
!  ==> Fatal error occurred, no output PDF file produced!
